/**
 * 12345政务服务热线系统 - 协办信息组件
 * @description 多部门协同工单的协办信息管理组件
 * <AUTHOR> Assistant
 * @date 2024-12-22
 */

/**
 * 协办信息组件
 */
const CollaborationComponent = {
    /**
     * 协办信息容器
     */
    container: null,
    
    /**
     * 协办数据
     */
    collaborationData: null,
    
    /**
     * 初始化协办信息组件
     * @param {string} containerId - 容器元素ID
     */
    initialize(containerId = 'collaborationInfo') {
        console.log('初始化协办信息组件');
        this.container = document.getElementById(containerId);
        
        if (!this.container) {
            console.error('找不到协办信息容器元素:', containerId);
            return;
        }
        
        // 加载协办数据
        this.loadCollaborationData();

        // 渲染协办信息
        this.render();
    },
    
    /**
     * 检查是否为协办工单
     * @returns {boolean} 是否为协办工单
     */
    isCollaborationTicket() {
        // 默认显示协办信息
        return true;
    },
    

    
    /**
     * 加载协办数据
     */
    loadCollaborationData() {
        // 使用统一的Mock数据
        if (window.MockData && window.MockData.collaboration) {
            const collab = window.MockData.collaboration;
            this.collaborationData = {
                mode: 'multi_department_collaboration',
                mainDepartment: {
                    id: 'dept001',
                    name: collab.mainHandler.department,
                    contact: collab.mainHandler.contact,
                    phone: collab.mainHandler.phone,
                    role: 'main',
                    status: 'processing',
                    acceptTime: '2024-12-20 14:30:00',
                    tasks: [
                        '统筹协调各部门处理',
                        '现场检查和监督',
                        '汇总处理结果'
                    ]
                },
                collaborators: collab.collaborators.map((collaborator, index) => ({
                    id: `dept00${index + 2}`,
                    name: collaborator.department,
                    contact: collaborator.contact,
                    phone: collaborator.phone,
                    role: 'collaborator',
                    status: collaborator.status,
                    acceptTime: collaborator.status === 'completed' ? '2024-12-21 10:00:00' : null,
                    submitTime: collaborator.submitTime,
                    opinion: collaborator.opinion,
                    tasks: [collaborator.role]
                })),
                summary: collab.summary
            };
        } else {
            // 备用数据
            this.collaborationData = {
            mode: 'multi_department_collaboration', // 多部门协同模式
            mainDepartment: {
                id: 'dept001',
                name: '城市管理局',
                contact: '王五 (工号: A001)',
                phone: '13900001001',
                role: 'main',
                status: 'processing',
                acceptTime: '2024-12-22 11:05:47',
                tasks: [
                    '统筹协调各部门处理',
                    '现场检查和监督',
                    '汇总处理结果'
                ]
            },
            collaborators: [
                {
                    id: 'dept002',
                    name: '环保局',
                    contact: '李七 (工号: B001)',
                    phone: '13900002001',
                    role: 'collaborator',
                    status: 'completed',
                    inviteTime: '2024-12-22 14:30:00',
                    acceptTime: '2024-12-22 15:15:22',
                    completeTime: '2024-12-23 16:30:45',
                    tasks: [
                        '环境污染评估',
                        '提供环保整改建议'
                    ],
                    result: '经检测，该区域环境指标正常，建议加强日常监管。'
                },
                {
                    id: 'dept003',
                    name: '住建局',
                    contact: '张八 (工号: C001)',
                    phone: '13900003001',
                    role: 'collaborator',
                    status: 'processing',
                    inviteTime: '2024-12-22 14:30:00',
                    acceptTime: '2024-12-22 16:20:15',
                    completeTime: null,
                    tasks: [
                        '物业管理监督',
                        '小区设施维护指导'
                    ],
                    result: null
                },
                {
                    id: 'dept004',
                    name: '街道办事处',
                    contact: '赵九 (工号: D001)',
                    phone: '13900004001',
                    role: 'collaborator',
                    status: 'pending',
                    inviteTime: '2024-12-23 09:00:00',
                    acceptTime: null,
                    completeTime: null,
                    tasks: [
                        '社区协调',
                        '居民沟通'
                    ],
                    result: null
                }
            ],
            progress: {
                total: 4,
                completed: 1,
                processing: 2,
                pending: 1,
                percentage: 25
            },
            communications: [
                {
                    id: 'comm001',
                    time: '2024-12-22 14:30:00',
                    type: 'invite',
                    from: '城市管理局',
                    to: '环保局',
                    content: '邀请协办：请协助进行环境污染评估',
                    status: 'sent'
                },
                {
                    id: 'comm002',
                    time: '2024-12-22 15:15:22',
                    type: 'accept',
                    from: '环保局',
                    to: '城市管理局',
                    content: '已接收协办任务，将安排专人处理',
                    status: 'received'
                },
                {
                    id: 'comm003',
                    time: '2024-12-23 16:30:45',
                    type: 'result',
                    from: '环保局',
                    to: '城市管理局',
                    content: '协办任务已完成，检测结果已提交',
                    status: 'received'
                }
            ]
        };
        }
    },
    
    /**
     * 渲染协办信息
     */
    render() {
        if (!this.container || !this.collaborationData) return;
        
        // 清空容器
        this.container.innerHTML = '';
        
        // 创建协办结构区域
        const structureSection = this.createStructureSection();
        this.container.appendChild(structureSection);
        
        // 创建进度跟踪区域
        const progressSection = this.createProgressSection();
        this.container.appendChild(progressSection);
        
        // 创建协办列表区域
        const collaboratorsSection = this.createCollaboratorsSection();
        this.container.appendChild(collaboratorsSection);
        
        // 创建沟通记录区域
        const communicationsSection = this.createCommunicationsSection();
        this.container.appendChild(communicationsSection);
    },
    
    /**
     * 创建协办结构区域
     * @returns {HTMLElement} 协办结构元素
     */
    createStructureSection() {
        const section = document.createElement('div');
        section.className = 'collaboration-structure';
        
        const main = this.collaborationData.mainDepartment;
        
        section.innerHTML = `
            <div class="structure-header">
                <h4><i class="fas fa-sitemap"></i> 协办结构</h4>
            </div>
            <div class="main-department">
                <div class="department-card main">
                    <div class="department-header">
                        <span class="department-role">主办部门</span>
                        <span class="department-status status-${main.status}">${this.getStatusText(main.status)}</span>
                    </div>
                    <div class="department-info">
                        <div class="department-name">${main.name}</div>
                        <div class="department-contact">
                            <i class="fas fa-user"></i> ${main.contact}
                            <i class="fas fa-phone"></i> ${main.phone}
                        </div>
                        <div class="department-tasks">
                            <div class="tasks-label">主要职责：</div>
                            <ul class="tasks-list">
                                ${main.tasks.map(task => `<li>${task}</li>`).join('')}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        return section;
    },
    
    /**
     * 创建进度跟踪区域
     * @returns {HTMLElement} 进度跟踪元素
     */
    createProgressSection() {
        const section = document.createElement('div');
        section.className = 'collaboration-progress';
        
        // 计算进度
        const totalCollaborators = this.collaborationData.collaborators.length;
        const completedCollaborators = this.collaborationData.collaborators.filter(c => c.status === 'completed').length;
        const progress = {
            percentage: totalCollaborators > 0 ? Math.round((completedCollaborators / totalCollaborators) * 100) : 0,
            completed: completedCollaborators,
            total: totalCollaborators
        };
        
        section.innerHTML = `
            <div class="progress-header">
                <h4><i class="fas fa-tasks"></i> 整体进度</h4>
                <span class="progress-percentage">${progress.percentage}%</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: ${progress.percentage}%"></div>
            </div>
            <div class="progress-stats">
                <div class="stat-item">
                    <span class="stat-value">${progress.completed}</span>
                    <span class="stat-label">已完成</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value">${progress.processing}</span>
                    <span class="stat-label">进行中</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value">${progress.pending}</span>
                    <span class="stat-label">待接收</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value">${progress.total}</span>
                    <span class="stat-label">总计</span>
                </div>
            </div>
        `;
        
        return section;
    },
    
    /**
     * 创建协办列表区域
     * @returns {HTMLElement} 协办列表元素
     */
    createCollaboratorsSection() {
        const section = document.createElement('div');
        section.className = 'collaboration-list';
        
        const header = document.createElement('div');
        header.className = 'list-header';
        header.innerHTML = `
            <h4><i class="fas fa-users"></i> 协办部门列表</h4>
            <button class="btn btn-sm btn-primary" onclick="CollaborationComponent.inviteCollaborator()">
                <i class="fas fa-plus"></i> 邀请协办
            </button>
        `;
        
        const list = document.createElement('div');
        list.className = 'collaborators-list';
        
        this.collaborationData.collaborators.forEach(collaborator => {
            const collaboratorElement = this.createCollaboratorElement(collaborator);
            list.appendChild(collaboratorElement);
        });
        
        section.appendChild(header);
        section.appendChild(list);
        
        return section;
    },
    
    /**
     * 创建协办部门元素
     * @param {Object} collaborator - 协办部门数据
     * @returns {HTMLElement} 协办部门元素
     */
    createCollaboratorElement(collaborator) {
        const element = document.createElement('div');
        element.className = `collaborator-item status-${collaborator.status}`;
        
        element.innerHTML = `
            <div class="collaborator-header">
                <div class="collaborator-info">
                    <span class="collaborator-name">${collaborator.name}</span>
                    <span class="collaborator-status status-${collaborator.status}">
                        ${this.getStatusText(collaborator.status)}
                    </span>
                </div>
                <div class="collaborator-actions">
                    ${this.getCollaboratorActions(collaborator)}
                </div>
            </div>
            <div class="collaborator-details">
                <div class="detail-row">
                    <span class="detail-label">联系人：</span>
                    <span class="detail-value">${collaborator.contact}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">联系电话：</span>
                    <span class="detail-value">${collaborator.phone}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">邀请时间：</span>
                    <span class="detail-value">${collaborator.inviteTime}</span>
                </div>
                ${collaborator.acceptTime ? `
                    <div class="detail-row">
                        <span class="detail-label">接收时间：</span>
                        <span class="detail-value">${collaborator.acceptTime}</span>
                    </div>
                ` : ''}
                ${collaborator.completeTime ? `
                    <div class="detail-row">
                        <span class="detail-label">完成时间：</span>
                        <span class="detail-value">${collaborator.completeTime}</span>
                    </div>
                ` : ''}
                <div class="detail-row">
                    <span class="detail-label">协办任务：</span>
                    <div class="detail-value">
                        <ul class="tasks-list">
                            ${collaborator.tasks.map(task => `<li>${task}</li>`).join('')}
                        </ul>
                    </div>
                </div>
                ${collaborator.result ? `
                    <div class="detail-row">
                        <span class="detail-label">处理结果：</span>
                        <div class="detail-value collaboration-result">
                            ${collaborator.result}
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
        
        return element;
    },
    
    /**
     * 获取协办部门操作按钮
     * @param {Object} collaborator - 协办部门数据
     * @returns {string} 操作按钮HTML
     */
    getCollaboratorActions(collaborator) {
        let actions = '';
        
        switch (collaborator.status) {
            case 'pending':
                actions = `
                    <button class="btn btn-sm btn-warning" onclick="CollaborationComponent.remindCollaborator('${collaborator.id}')">
                        <i class="fas fa-bell"></i> 催办
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="CollaborationComponent.cancelInvitation('${collaborator.id}')">
                        <i class="fas fa-times"></i> 取消
                    </button>
                `;
                break;
            case 'processing':
                actions = `
                    <button class="btn btn-sm btn-info" onclick="CollaborationComponent.contactCollaborator('${collaborator.id}')">
                        <i class="fas fa-phone"></i> 联系
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="CollaborationComponent.remindCollaborator('${collaborator.id}')">
                        <i class="fas fa-bell"></i> 催办
                    </button>
                `;
                break;
            case 'completed':
                actions = `
                    <button class="btn btn-sm btn-success" onclick="CollaborationComponent.viewResult('${collaborator.id}')">
                        <i class="fas fa-eye"></i> 查看结果
                    </button>
                `;
                break;
        }
        
        return actions;
    },
    
    /**
     * 创建沟通记录区域
     * @returns {HTMLElement} 沟通记录元素
     */
    createCommunicationsSection() {
        const section = document.createElement('div');
        section.className = 'collaboration-communications';
        
        const header = document.createElement('div');
        header.className = 'communications-header';
        header.innerHTML = `
            <h4><i class="fas fa-comments"></i> 沟通记录</h4>
        `;
        
        const list = document.createElement('div');
        list.className = 'communications-list';
        
        const communications = this.collaborationData.communications || [
            {
                id: 'comm001',
                type: 'coordination',
                time: '2024-12-21 10:30:00',
                from: '城市管理局',
                to: '住建局',
                content: '请协助提供技术指导意见',
                status: 'completed'
            },
            {
                id: 'comm002',
                type: 'feedback',
                time: '2024-12-22 14:30:00',
                from: '住建局',
                to: '城市管理局',
                content: '已完成技术评估，建议分批次改造',
                status: 'completed'
            }
        ];

        communications.forEach(comm => {
            const commElement = document.createElement('div');
            commElement.className = `communication-item type-${comm.type}`;
            
            const typeInfo = this.getCommunicationTypeInfo(comm.type);
            
            commElement.innerHTML = `
                <div class="communication-icon">
                    <i class="${typeInfo.icon}"></i>
                </div>
                <div class="communication-content">
                    <div class="communication-header">
                        <span class="communication-type">${typeInfo.label}</span>
                        <span class="communication-time">${comm.time}</span>
                    </div>
                    <div class="communication-parties">
                        <span class="from">${comm.from}</span>
                        <i class="fas fa-arrow-right"></i>
                        <span class="to">${comm.to}</span>
                    </div>
                    <div class="communication-text">${comm.content}</div>
                </div>
            `;
            
            list.appendChild(commElement);
        });
        
        section.appendChild(header);
        section.appendChild(list);
        
        return section;
    },
    
    /**
     * 获取状态文本
     * @param {string} status - 状态值
     * @returns {string} 状态文本
     */
    getStatusText(status) {
        const statusMap = {
            pending: '待接收',
            processing: '进行中',
            completed: '已完成',
            rejected: '已拒绝'
        };
        
        return statusMap[status] || '未知';
    },
    
    /**
     * 获取沟通类型信息
     * @param {string} type - 沟通类型
     * @returns {Object} 类型信息
     */
    getCommunicationTypeInfo(type) {
        const typeMap = {
            invite: { label: '邀请协办', icon: 'fas fa-paper-plane' },
            accept: { label: '接收协办', icon: 'fas fa-check' },
            reject: { label: '拒绝协办', icon: 'fas fa-times' },
            result: { label: '提交结果', icon: 'fas fa-file-upload' },
            remind: { label: '催办提醒', icon: 'fas fa-bell' },
            contact: { label: '沟通联系', icon: 'fas fa-phone' }
        };
        
        return typeMap[type] || { label: '其他', icon: 'fas fa-comment' };
    },
    
    /**
     * 邀请协办部门
     */
    inviteCollaborator() {
        Utils.showToast('打开邀请协办部门界面', 'info');
        // 这里实现邀请协办的逻辑
    },
    
    /**
     * 催办协办部门
     * @param {string} collaboratorId - 协办部门ID
     */
    remindCollaborator(collaboratorId) {
        const collaborator = this.collaborationData.collaborators.find(c => c.id === collaboratorId);
        if (!collaborator) return;
        
        Utils.showToast(`正在催办 ${collaborator.name}...`, 'info');
        // 这里实现催办逻辑
    },
    
    /**
     * 联系协办部门
     * @param {string} collaboratorId - 协办部门ID
     */
    contactCollaborator(collaboratorId) {
        const collaborator = this.collaborationData.collaborators.find(c => c.id === collaboratorId);
        if (!collaborator) return;
        
        Utils.showToast(`正在联系 ${collaborator.name}...`, 'info');
        // 这里实现联系逻辑
    },
    
    /**
     * 取消邀请
     * @param {string} collaboratorId - 协办部门ID
     */
    cancelInvitation(collaboratorId) {
        const collaborator = this.collaborationData.collaborators.find(c => c.id === collaboratorId);
        if (!collaborator) return;
        
        if (!confirm(`确定要取消对 ${collaborator.name} 的协办邀请吗？`)) return;
        
        Utils.showToast(`已取消对 ${collaborator.name} 的邀请`, 'success');
        // 这里实现取消邀请逻辑
    },
    
    /**
     * 查看协办结果
     * @param {string} collaboratorId - 协办部门ID
     */
    viewResult(collaboratorId) {
        const collaborator = this.collaborationData.collaborators.find(c => c.id === collaboratorId);
        if (!collaborator) return;
        
        Utils.showToast(`查看 ${collaborator.name} 的协办结果`, 'info');
        // 这里实现查看结果逻辑
    },
    
    /**
     * 更新协办数据
     * @param {Object} newData - 新的协办数据
     */
    updateCollaborationData(newData) {
        this.collaborationData = { ...this.collaborationData, ...newData };
        this.render();
    },
    
    /**
     * 获取协办数据
     * @returns {Object} 协办数据
     */
    getCollaborationData() {
        return this.collaborationData;
    }
};

// 将CollaborationComponent暴露到全局作用域
window.CollaborationComponent = CollaborationComponent;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    CollaborationComponent.initialize();
});
