<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>业务流程 - 12345智慧政务平台</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/workflow.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header组件容器 -->
    <div id="header-container"></div>

    <main class="main">
        <section class="page-header">
            <div class="container">
                <h1 class="page-title">业务流程</h1>
                <p class="page-subtitle">五阶段完整流程，条块结合分派，多部门协同处理</p>
            </div>
        </section>

        <section class="workflow-overview">
            <div class="container">
                <div class="workflow-stats">
                    <div class="stat-item">
                        <div class="stat-number">5</div>
                        <div class="stat-label">业务阶段</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">3</div>
                        <div class="stat-label">处理轨道</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">多层级</div>
                        <div class="stat-label">即时办结</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">原路返回</div>
                        <div class="stat-label">审核机制</div>
                    </div>
                </div>
            </div>
        </section>

        <section class="workflow-stages">
            <div class="container">
                <h2 class="section-title">完整业务流程</h2>
                
                <!-- 阶段一：市级统一受理与顶层战略分派 -->
                <div class="stage-section">
                    <div class="stage-header">
                        <div class="stage-number">1</div>
                        <div class="stage-info">
                            <h3 class="stage-title">市级统一受理与顶层战略分派</h3>
                            <p class="stage-subtitle">工单的诞生与初始分流</p>
                        </div>
                    </div>
                    
                    <div class="stage-content">
                        <div class="process-steps">
                            <div class="process-step">
                                <div class="step-icon">
                                    <i class="fas fa-phone"></i>
                                </div>
                                <div class="step-content">
                                    <h4>受理创建</h4>
                                    <ul>
                                        <li>市民通过12345热线提出诉求</li>
                                        <li>市级12345中心话务员接听电话，在系统中新建工单</li>
                                        <li>录入所有标准化信息</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="process-step">
                                <div class="step-icon">
                                    <i class="fas fa-route"></i>
                                </div>
                                <div class="step-content">
                                    <h4>一级分派决策</h4>
                                    <p class="step-description">市级层面的"条块"战略选择</p>
                                    
                                    <div class="scenarios">
                                        <div class="scenario immediate">
                                            <h5><i class="fas fa-bolt"></i> 场景A：即时办结</h5>
                                            <p>对于"政策咨询"、"信息查询"、"简单告知"等诉求，话务员利用知识库直接答复市民，选择"即时办结"，工单跳过所有下派环节，直接进入【待回访】状态。</p>
                                        </div>
                                        
                                        <div class="scenario department">
                                            <h5><i class="fas fa-building"></i> 场景B：派发至市级职能部门</h5>
                                            <p>适用于涉及全市范围的宏观政策、跨区域的重大事件、或明确由市级部门垂直管理的事项。</p>
                                        </div>
                                        
                                        <div class="scenario district">
                                            <h5><i class="fas fa-map-marker-alt"></i> 场景C：派发至区/县级总口</h5>
                                            <p>绝大部分涉及具体地点、具体民生、需要属地化管理的诉求，根据地域属性派发至对应的区/县级12345分中心。</p>
                                        </div>
                                        
                                        <div class="scenario collaboration">
                                            <h5><i class="fas fa-handshake"></i> 场景D：市级层面的多部门协同</h5>
                                            <p>需要市级部门与区级政府协同的重大复杂问题，构建多部门协同，确定主办部门和协办部门。</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 阶段二：工单进入不同轨道的后续流转 -->
                <div class="stage-section">
                    <div class="stage-header">
                        <div class="stage-number">2</div>
                        <div class="stage-info">
                            <h3 class="stage-title">工单进入不同轨道的后续流转</h3>
                            <p class="stage-subtitle">多轨道并行处理</p>
                        </div>
                    </div>
                    
                    <div class="stage-content">
                        <div class="tracks">
                            <div class="track municipal">
                                <h4><i class="fas fa-building"></i> 轨道一：市级职能部门内部流转</h4>
                                <div class="track-steps">
                                    <div class="track-step">
                                        <span class="step-number">3</span>
                                        <div class="step-info">
                                            <strong>市级部门接收与分派</strong>
                                            <p>市级职能部门总口接收工单，判断能否即时办结，如不能则下派至内部具体的业务处室/科室</p>
                                        </div>
                                    </div>
                                    <div class="track-step">
                                        <span class="step-number">4</span>
                                        <div class="step-info">
                                            <strong>市级科室处理与执行</strong>
                                            <p>市级业务科室接收工单，判断能否即时办结，如不能则指派给具体的工作人员</p>
                                        </div>
                                    </div>
                                    <div class="track-step">
                                        <span class="step-number">5</span>
                                        <div class="step-info">
                                            <strong>市级工作人员执行</strong>
                                            <p>工作人员执行任务，在系统中补记过程，任务完成后点击"办结"</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="track district">
                                <h4><i class="fas fa-map-marker-alt"></i> 轨道二：下沉至区/县后的复杂流转</h4>
                                <div class="track-steps">
                                    <div class="track-step">
                                        <span class="step-number">3</span>
                                        <div class="step-info">
                                            <strong>区级总口接收与分派</strong>
                                            <p>区/县12345分中心接收工单，判断能否即时办结，如不能则进行区内二次分派</p>
                                            <div class="sub-options">
                                                <span class="option">A. 派发至街/镇（属地路线）</span>
                                                <span class="option">B. 派发至区职能部门（职能路线）</span>
                                                <span class="option">C. 构建区级多部门协同</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="track-step">
                                        <span class="step-number">4</span>
                                        <div class="step-info">
                                            <strong>区级下沉后的处理与执行</strong>
                                            <div class="parallel-paths">
                                                <div class="path">
                                                    <strong>属地路线：</strong>
                                                    <p>街/镇 → 社区/村委会 → 网格员执行</p>
                                                </div>
                                                <div class="path">
                                                    <strong>职能路线：</strong>
                                                    <p>区级职能部门 → 业务科室 → 一线工作人员执行</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 阶段三：工单的逐级下沉与多重办结机会 -->
                <div class="stage-section">
                    <div class="stage-header">
                        <div class="stage-number">3</div>
                        <div class="stage-info">
                            <h3 class="stage-title">工单的逐级下沉与多重办结机会</h3>
                            <p class="stage-subtitle">多轨道并行下派与逐级判断</p>
                        </div>
                    </div>
                    
                    <div class="stage-content">
                        <div class="process-steps">
                            <div class="process-step">
                                <div class="step-icon">
                                    <i class="fas fa-sitemap"></i>
                                </div>
                                <div class="step-content">
                                    <h4>多轨道并行下派与逐级判断</h4>
                                    <p>工单沿着各自的轨道下派，每一个接收到工单的层级，在进行下一步分派前，都必须先进行"能否即时办结"的判断。</p>
                                    <div class="highlight-box">
                                        <i class="fas fa-lightbulb"></i>
                                        <p>只有在确认本级无法独立解决时，才会继续向下派发</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="process-step">
                                <div class="step-icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="step-content">
                                    <h4>最终执行与办结</h4>
                                    <p>若工单最终下沉至最终执行者（网格员/工作人员），则由其前往现场处理并"办结"，触发审核流程。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 阶段四：逐级审核反馈与闭环 -->
                <div class="stage-section">
                    <div class="stage-header">
                        <div class="stage-number">4</div>
                        <div class="stage-info">
                            <h3 class="stage-title">逐级审核反馈与闭环</h3>
                            <p class="stage-subtitle">原路返回，质量把控</p>
                        </div>
                    </div>
                    
                    <div class="stage-content">
                        <div class="process-steps">
                            <div class="process-step">
                                <div class="step-icon">
                                    <i class="fas fa-play"></i>
                                </div>
                                <div class="step-content">
                                    <h4>启动审核</h4>
                                    <p>任何一个层级的"执行办结"或"即时办结"操作完成后，工单都进入【待审核】状态。</p>
                                </div>
                            </div>
                            
                            <div class="process-step">
                                <div class="step-icon">
                                    <i class="fas fa-undo"></i>
                                </div>
                                <div class="step-content">
                                    <h4>逐级审核（谁派发，谁审核）</h4>
                                    <p>工单严格按照其"来时"的路径，自下而上、原路返回。每一级的管理者都需要对下一级提交的办结结果进行审核。</p>
                                    
                                    <div class="audit-examples">
                                        <div class="audit-path">
                                            <h5>审核路径示例1（属地路线）：</h5>
                                            <div class="path-flow">
                                                <span class="path-step">网格员办结</span>
                                                <i class="fas fa-arrow-right"></i>
                                                <span class="path-step">社区审核</span>
                                                <i class="fas fa-arrow-right"></i>
                                                <span class="path-step">街道审核</span>
                                                <i class="fas fa-arrow-right"></i>
                                                <span class="path-step">区级分中心审核</span>
                                            </div>
                                        </div>
                                        
                                        <div class="audit-path">
                                            <h5>审核路径示例2（职能路线）：</h5>
                                            <div class="path-flow">
                                                <span class="path-step">区工作人员办结</span>
                                                <i class="fas fa-arrow-right"></i>
                                                <span class="path-step">区业务科室审核</span>
                                                <i class="fas fa-arrow-right"></i>
                                                <span class="path-step">区职能部门审核</span>
                                                <i class="fas fa-arrow-right"></i>
                                                <span class="path-step">区级分中心审核</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="process-step">
                                <div class="step-icon">
                                    <i class="fas fa-upload"></i>
                                </div>
                                <div class="step-content">
                                    <h4>向市级平台反馈</h4>
                                    <p>无论是从市级职能部门办结返回，还是从区/县12345分中心办结返回，最终都会向市级平台提交"办结反馈"，工单状态统一更新为【待回访】。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 阶段五：市级统一回访与最终关闭 -->
                <div class="stage-section">
                    <div class="stage-header">
                        <div class="stage-number">5</div>
                        <div class="stage-info">
                            <h3 class="stage-title">市级统一回访与最终关闭</h3>
                            <p class="stage-subtitle">服务质量检验与闭环管理</p>
                        </div>
                    </div>
                    
                    <div class="stage-content">
                        <div class="process-steps">
                            <div class="process-step">
                                <div class="step-icon">
                                    <i class="fas fa-phone-alt"></i>
                                </div>
                                <div class="step-content">
                                    <h4>独立回访</h4>
                                    <p>市级回访中心对所有进入【待回访】状态的工单，统一进行回访。</p>
                                </div>
                            </div>
                            
                            <div class="process-step">
                                <div class="step-icon">
                                    <i class="fas fa-check-double"></i>
                                </div>
                                <div class="step-content">
                                    <h4>最终闭环</h4>
                                    <div class="final-outcomes">
                                        <div class="outcome satisfied">
                                            <i class="fas fa-smile"></i>
                                            <div>
                                                <strong>市民满意：</strong>
                                                <p>回访员将工单【已关闭】</p>
                                            </div>
                                        </div>
                                        <div class="outcome unsatisfied">
                                            <i class="fas fa-frown"></i>
                                            <div>
                                                <strong>市民不满意：</strong>
                                                <p>回访员重启工单，附上"重办督办"意见，退回至市级12345中心，启动新一轮的、更高级别的督办处理流程</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 多部门协同流程 -->
        <section class="collaboration-section">
            <div class="container">
                <h2 class="section-title">多部门协同流程详解</h2>
                
                <div class="collaboration-types">
                    <div class="collaboration-type">
                        <h3><i class="fas fa-city"></i> 市级多部门协同流程</h3>
                        <div class="collaboration-content">
                            <div class="scenario-info">
                                <strong>启动场景：</strong>跨区域、跨部门的重大复杂问题，需要多个市级部门和区级政府协同处理。
                            </div>
                            <div class="example-case">
                                <strong>示例场景：</strong>跨多个区域的河流污染问题，涉及环境监测、水务管理、属地处置等多个方面。
                            </div>
                            <div class="collaboration-steps">
                                <div class="collab-step">
                                    <span class="step-num">1</span>
                                    <div class="step-detail">
                                        <h4>构建协同结构</h4>
                                        <p>确定主办部门（最相关的市级职能部门），添加协办部门（其他相关市级部门和涉及区域的12345分中心）</p>
                                    </div>
                                </div>
                                <div class="collab-step">
                                    <span class="step-num">2</span>
                                    <div class="step-detail">
                                        <h4>多方并行处理</h4>
                                        <p>主办部门负责专业调查、技术鉴定和总协调；协办部门各自承担相应的专业支持或现场配合工作</p>
                                    </div>
                                </div>
                                <div class="collab-step">
                                    <span class="step-num">3</span>
                                    <div class="step-detail">
                                        <h4>信息汇总与联合处置</h4>
                                        <p>主办部门在确认所有问题均已解决、各方均已完成工作后，汇总所有处理情况，形成最终的办结报告</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer组件容器 -->
    <div id="footer-container"></div>

    <script src="js/components.js"></script>
    <script src="js/script.js"></script>
    <script src="js/workflow.js"></script>
</body>
</html>
